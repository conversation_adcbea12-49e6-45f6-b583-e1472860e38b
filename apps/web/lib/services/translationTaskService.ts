import { BusinessError } from "@lib/errors"
import { prisma } from "@repo/db"
import logger from "@repo/logger"
import {
	ProjectGameLocaleType,
	ProjectLocaleSiteSettingType,
} from "@repo/shared-types"
import { diff } from "@repo/utils"
import { createBackgroundTask } from "./BackgroundTaskService"

/**
 * 翻译任务相关的接口定义
 */
export interface TranslationTaskParams {
	// 翻译数据 ID  => ProjectLocaleSiteSetting 、ProjectGameLocale 、ProjectArticleLocale
	contentId: string
	// 翻译的内容类型 (metadata, content, etc.)
	contentType: string
	// 源语言
	sourceLocale: string
	// 目标语言列表
	targetLocales: string[]
	// 需要翻译的字段
	fieldsToTranslate?: string[]
	// 额外参数
	[key: string]: any
}

export interface TranslationTaskInfo {
	// 翻译数据 ID  => ProjectLocaleSiteSetting 、ProjectGameLocale 、ProjectArticleLocale
	contentId: string
	// 翻译数据类型
	contentType: string
	// 翻译数据
	content: any
	/** 原始内容，用于变更检测 */
	originalContent?: any
	/** 是否强制创建翻译任务，忽略变更检测 */
	force?: boolean
}

export interface TranslationLanguage {
	sourceLocale: string
	targetLocales: string[]
}

/**
 * 根据游戏内容类型获取需要翻译的字段
 */
export function getFieldsToTranslate(type: string): string[] {
	switch (type) {
		case "metadata":
			return [
				"title",
				"name",
				"description",
				"ogTitle",
				"ogDescription",
				"twitterTitle",
				"twitterDescription",
			]
		case "content":
			return ["title", "jsonContent", "text"]
		case "basicInfo":
			return ["gameName", "gameDescription", "name", "description"]
		default:
			// 对于未知类型，尝试翻译常见的文本字段
			return ["title", "name", "description", "content", "text"]
	}
}

/**
 * 创建翻译任务
 *
 * @param type 翻译任务类型
 * @param projectId 项目ID
 * @param userId 用户ID
 * @param params 翻译任务参数
 * @param queueName 可选的队列名称
 * @returns 翻译任务ID
 */
export async function createTranslationTask(
	type: ProjectLocaleSiteSettingType | ProjectGameLocaleType | string,
	projectId: string,
	userId: string,
	params: TranslationTaskParams,
	queueName?: string,
): Promise<string> {
	// scheduler 监听翻译任务时，会根据type区分不同的翻译任务，查询不同的数据表。需要确保type的值不冲突
	// 约定格式：ProjectSiteSetting_${ProjectLocaleSiteSettingType} 和 ProjectGame_${ProjectGameLocaleType}
	logger.info(`创建翻译任务`, { projectId, userId, params })
	const { contentType, contentId, sourceLocale, targetLocales } = params

	const title = `翻译任务: ${contentType} (${contentId}) - ${type}`
	const description = `将 ${sourceLocale} 翻译为 ${targetLocales.join(", ")}`

	let joinType = type
	if (type in Object.values(ProjectLocaleSiteSettingType)) {
		joinType = `ProjectSiteSetting_${type}`
	} else if (type in Object.values(ProjectGameLocaleType)) {
		joinType = `ProjectGame_${type}`
	} else {
		throw new BusinessError(`type is not valid => ${type}`)
	}

	return await createBackgroundTask({
		queueName: `${process.env.TASK_QUEUE_NAME}_translation_tasks`,
		type: joinType,
		projectId,
		userId,
		title,
		description,
		parameters: params,
	})
}



/**
 * 判断项目设置是否需要创建翻译任务
 */
async function shouldCreateProjectSettingTranslationTask(
	projectId: string,
	type: string,
	locale: string,
	newContent: any,
	existingSettingId?: string,
): Promise<{ shouldTranslate: boolean; targetLocales: string[] }> {
	// 获取项目的语言设置
	const siteSettings = await prisma.projectSiteSetting.findUnique({
		where: { projectId },
		select: {
			languanges: true,
			defaultLocale: true,
		},
	})
	if (!siteSettings) {
		logger.error("Project site settings not found", { projectId })
		throw new Error("Project site settings not found")
	}

	const languages = siteSettings.languanges as string[]
	const defaultLanguage = siteSettings.defaultLocale

	if (!languages || !Array.isArray(languages) || languages.length <= 1) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	// 如果当前语言不是默认语言，不创建翻译任务
	if (locale !== defaultLanguage) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	// 获取需要翻译的目标语言
	const targetLocales = languages.filter((lang) => lang !== defaultLanguage)
	if (targetLocales.length === 0) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	// 如果是更新操作，检查内容是否有变化
	if (existingSettingId) {
		const existingSetting = await prisma.projectLocaleSiteSetting.findUnique({
			where: { id: existingSettingId },
			select: { content: true },
		})

		// if (existingSetting) {
		// 	// 检查需要翻译的字段是否有变化
		// 	const fieldsToTranslate = getFieldsToTranslate(type)
		// 	const hasChanges = fieldsToTranslate.some((field) => {
		// 		const oldValue = getNestedValue(existingSetting.content, field)
		// 		const newValue = getNestedValue(newContent, field)
		// 		return oldValue !== newValue
		// 	})
		// 	logger.info(
		// 		`当前网站设置,type: ${type} 有变化: ${hasChanges},需要翻译的目标语言: ${targetLocales}`,
		// 	)
		// 	if (!hasChanges) {
		// 		return { shouldTranslate: false, targetLocales: [] }
		// 	}
		// }
	}

	// 检查其他语言的内容是否为空
	const existingTranslations = await prisma.projectLocaleSiteSetting.findMany({
		where: {
			projectId,
			type,
			locale: { in: targetLocales },
		},
		select: { locale: true, content: true },
	})

	// 找出需要翻译的语言（内容为空或不存在的语言）
	const localesNeedingTranslation = targetLocales.filter((targetLocale) => {
		const existingTranslation = existingTranslations.find(
			(t) => t.locale === targetLocale,
		)
		if (!existingTranslation) return true

		// 检查是否有任何需要翻译的字段为空
		const fieldsToTranslate = getFieldsToTranslate(type)
		return fieldsToTranslate.some((field) => {
			const value = getNestedValue(existingTranslation.content, field)
			return !value || (typeof value === "string" && value.trim() === "")
		})
	})
	logger.info(
		`当前网站设置,type: ${type} 存在缺失翻译的目标语言: ${localesNeedingTranslation} ，需要异步创建翻译任务`,
	)
	return {
		shouldTranslate: localesNeedingTranslation.length > 0,
		targetLocales: localesNeedingTranslation,
	}
}


async function shouldCreateGameTranslationTask(
	projectId: string,
	gameId: string,
	type: string,
	locale: string,
	newContent: any,
	existingContentId?: string,
): Promise<{ shouldTranslate: boolean; targetLocales: string[] }> {
	// 获取项目的语言设置
	const project = await prisma.project.findUnique({
		where: { id: projectId },
		include: {
			siteSettings: true,
		},
	})

	if (
		!project?.siteSettings ||
		!Array.isArray(project.siteSettings) ||
		project.siteSettings.length === 0
	) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	const siteSettings = project.siteSettings[0]
	if (!siteSettings) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	const languages = siteSettings.languanges as string[]
	const defaultLanguage = siteSettings.defaultLocale

	if (!languages || !Array.isArray(languages) || languages.length <= 1) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	// 如果当前语言不是默认语言，不创建翻译任务
	if (locale !== defaultLanguage) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	// 获取需要翻译的目标语言
	const targetLocales = languages.filter((lang) => lang !== defaultLanguage)
	if (targetLocales.length === 0) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	// 检查其他语言的内容是否为空
	const existingTranslations = await prisma.projectGameLocale.findMany({
		where: {
			projectId,
			gameId,
			type,
			locale: { in: targetLocales },
			...(existingContentId
				? {
						contentId: (
							await prisma.projectGameLocale.findUnique({
								where: { id: existingContentId },
								select: { contentId: true },
							})
						)?.contentId,
					}
				: {}),
		},
		select: { locale: true, content: true },
	})

	// 找出需要翻译的语言（内容为空或不存在的语言）
	const localesNeedingTranslation = targetLocales.filter((targetLocale) => {
		const existingTranslation = existingTranslations.find(
			(t) => t.locale === targetLocale,
		)
		if (!existingTranslation) return true

		// 检查是否有任何需要翻译的字段为空
		const fieldsToTranslate = getFieldsToTranslate(type)
		return fieldsToTranslate.some((field) => {
			const value = getNestedValue(existingTranslation.content, field)
			return !value || (typeof value === "string" && value.trim() === "")
		})
	})

	return {
		shouldTranslate: localesNeedingTranslation.length > 0,
		targetLocales: localesNeedingTranslation,
	}
}

/**
 * 创建单个翻译任务
 *
 * @param type 内容类型
 * @param projectId 项目ID
 * @param userId 用户ID
 * @returns 翻译任务ID，如果不需要创建则返回null
 */
export async function createSingleTranslationTask(
	type: string,
	projectId: string,
	userId: string,
	info: TranslationTaskInfo,
	language?: TranslationLanguage,
): Promise<string | null> {
	try {
		const {
			originalContent,
			force = false,
			content,
			contentType,
			contentId,
		} = info

		// 如果提供了原始内容且未强制创建，则进行变更检测
		if (originalContent && !force) {
			const fieldsToTranslate = getFieldsToTranslate(type)
			let hasChanges = false

			// 检查每个需要翻译的字段是否有变化
			for (const field of fieldsToTranslate) {
				const { hasDifferences } = diff(originalContent, content, field)
				if (hasDifferences) {
					hasChanges = true
					break
				}
			}

			// 如果没有变化，不创建翻译任务
			if (!hasChanges) {
				return null
			}
		}

		let sourceLocale = language?.sourceLocale
		let targetLocales = language?.targetLocales

		if (!sourceLocale || !targetLocales) {
			const setting = await prisma.projectSiteSetting.findUnique({
				where: { id: projectId },
				select: {
					defaultLocale: true,
					languanges: true,
				},
			})
			if (!setting) {
				throw new BusinessError(`projectSiteSetting not found`)
			}
			sourceLocale = setting.defaultLocale
			targetLocales = setting.languanges as any[]
		}

		// 构建翻译任务参数
		const translationParams: TranslationTaskParams = {
			contentType,
			contentId,
			sourceLocale,
			targetLocales,
			fieldsToTranslate: getFieldsToTranslate(type),
		}

		const translationTaskId = await createTranslationTask(
			type,
			projectId,
			userId,
			translationParams,
		)

		return translationTaskId
	} catch (error) {
		console.error("创建翻译任务失败:", error)
		return null
	}
}

/**
 * 批量创建翻译任务
 *
 * @param translationTasks 翻译任务信息列表
 * @param type 内容类型
 * @param projectId 项目ID
 * @param userId 用户ID
 * @param locale 源语言
 * @param gameParams 游戏相关参数（可选）
 * @param options 创建选项
 * @returns 创建的翻译任务ID列表
 */
export async function createBatchTranslationTasks(
	translationTasks: TranslationTaskInfo[],
	type: string,
	projectId: string,
	userId: string,
	locale: string,
	gameParams?: { gameId: string; gameName: string },
	options: CreateBatchTranslationTasksOptions = {},
): Promise<string[]> {
	const createdTranslationTasks: string[] = []
	const { enableChangeDetection = true } = options

	if (translationTasks.length === 0) {
		return createdTranslationTasks
	}

	for (const task of translationTasks) {
		try {
			let shouldCreateTranslation = false

			if (task.operation === "create") {
				// 新创建的内容总是需要翻译
				shouldCreateTranslation = true
			} else if (task.operation === "update") {
				if (enableChangeDetection && task.originalContent) {
					// 更新操作：使用diff比较内容是否有变化
					const fieldsToTranslate = getFieldsToTranslate(type)
					let hasChanges = false

					// 检查每个需要翻译的字段是否有变化
					for (const field of fieldsToTranslate) {
						const { hasDifferences } = diff(
							task.originalContent,
							task.content,
							field,
						)
						if (hasDifferences) {
							hasChanges = true
							break
						}
					}

					shouldCreateTranslation = hasChanges
				} else {
					// 如果未启用变更检测或没有原始内容，默认创建翻译任务
					shouldCreateTranslation = true
				}
			}

			if (shouldCreateTranslation) {
				// 使用统一的单个翻译任务创建接口
				const translationTaskId = await createSingleTranslationTask(
					type,
					projectId,
					userId,
					task.contentId,
					locale,
					task.content,
					gameParams,
					{
						originalContent: task.originalContent,
						forceCreate: !enableChangeDetection,
					},
				)

				if (translationTaskId) {
					createdTranslationTasks.push(translationTaskId)
				}
			}
		} catch (error) {
			console.error("创建翻译任务失败:", error)
			// 不影响主要的保存操作，只记录错误
		}
	}

	return createdTranslationTasks
}

// 为了保持向后兼容性，保留原有的游戏翻译任务函数
export async function createGameTranslationTasks(
	translationTasks: TranslationTaskInfo[],
	type: string,
	projectId: string,
	userId: string,
	gameId: string,
	gameName: string,
	locale: string,
): Promise<string[]> {
	return createBatchTranslationTasks(
		translationTasks,
		type,
		projectId,
		userId,
		locale,
		{ gameId, gameName },
		{ enableChangeDetection: true },
	)
}

// 为了保持向后兼容性，保留原有的单个游戏翻译任务函数
export async function createSingleGameTranslationTask(
	type: string,
	projectId: string,
	userId: string,
	gameId: string,
	gameName: string,
	locale: string,
	content: any,
	existingContentId?: string,
): Promise<string | null> {
	return createSingleTranslationTask(
		type,
		projectId,
		userId,
		existingContentId || "",
		locale,
		content,
		{ gameId, gameName },
		{ forceCreate: false },
	)
}
